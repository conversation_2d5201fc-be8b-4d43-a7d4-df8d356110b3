import os
from datetime import datetime, timedelta

import pendulum
from airflow import DAG
from airflow.providers.google.cloud.operators.kubernetes_engine import (
    GKEStartPodOperator,
)
from airflow.operators.python import PythonOperator
from airflow.models.taskinstance import TaskInstance
from shared_libs.default_args import get_default_args
from shared_libs.image_versions import get_full_image_name, get_gcr_registry 
from shared_libs.kubernetes import get_resource_size
from shared_libs.slack_callback import task_fail_slack_alert, task_notification_factory
from shared_libs.pagerduty_callback import dag_fail_pagerduty_alert

environment = os.environ.get("COMPOSER_ENVIRONMENT", "dev")
gcr_registry = get_gcr_registry(environment)

if environment != "prod":
    SCHEDULE_INTERVAL = "0 */6 * * *"  # Every 6 hours
    target_project = "algo-vms-dev-b64d7e"
    alert_slack_channels = ["#valuations-alerts-dev"]

else:
    SCHEDULE_INTERVAL = "*/30 * * * *"  # Every 30 minutes
    target_project = "algo-vms-prod-bd7bdf"
    alert_slack_channels = ["#valuations-alerts"]

common_name = "valuations-algo-vm-monitor"
namespace = common_name
service_account_name = common_name

custom_args = {
    "owner": "<EMAIL>",
    "start_date":  datetime(2020, 4, 1, tzinfo=pendulum.timezone("America/Los_Angeles")), 
    "retries": 0,
    "on_failure_callback": task_fail_slack_alert,
    "namespace": namespace,
    "service_account_name": service_account_name,
}
default_args = get_default_args(custom_args)

def check_unused_reservations_task_output(ti: TaskInstance):
    message = ti.xcom_pull(task_ids=check_unused_reservations.task_id)["message"]
    if message:
        raise Exception(message)

def generate_slack_alert(context, **kwargs):
    task_id = context["task"].task_id
    result = context["ti"].xcom_pull(task_ids=task_id)["message"]
    if result:
        for channel in alert_slack_channels:
            task_notification_factory(
                f""":x: Alert : Unused Reservation detected in {target_project}, Please check VM reservations.
                {result}
                """,
                channel=channel,
            )(context, **kwargs)


dag = DAG(
    common_name,
    default_args=default_args,
    description="Check GCP for VM reservations that are not being used",
    schedule_interval=SCHEDULE_INTERVAL,
    catchup=False,
    max_active_runs=1,
    on_failure_callback=dag_fail_pagerduty_alert("valuations_algo_vm_monitor_pagerduty_api_key"),
)

with dag:
    check_unused_reservations = GKEStartPodOperator(
        task_id=common_name.replace("-", "_"),
        name=common_name.replace("-", "_"),
        image=get_full_image_name(common_name, gcr_registry),
        container_resources=get_resource_size("Small"),
        arguments=["python3", "src/valuations-algo-vm-monitor.py"],
        env_vars={
            "TARGET_PROJECT": target_project,
        },
        get_logs=True,
        retries=1,
        retry_delay=timedelta(minutes=5),
        on_failure_callback=task_fail_slack_alert,
        on_success_callback=generate_slack_alert,
        do_xcom_push=True,
    )

check_unused_reservations_task = PythonOperator(
    task_id="check_unused_reservations_task",
    python_callable=check_unused_reservations_task_output,
    provide_context=True,
    dag=dag,
)



check_unused_reservations >> check_unused_reservations_task