#!/bin/bash

# sync-branch.sh - Automate syncing feature branch with master/main
# Usage: ./sync-branch.sh
# 
# This script automatically syncs the current feature branch with master,
# resolving CircleCI build failures caused by outdated branches.

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}🔄 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    print_error "Not in a git repository!"
    exit 1
fi

# Get current branch name
current_branch=$(git branch --show-current)

if [ -z "$current_branch" ]; then
    print_error "Could not determine current branch name."
    exit 1
fi

print_status "Current branch: $current_branch"

# Check if already on master/main
if [ "$current_branch" = "master" ] || [ "$current_branch" = "main" ]; then
    print_error "Already on master/main branch. Nothing to sync."
    exit 1
fi

# Check for uncommitted changes
if ! git diff --quiet || ! git diff --cached --quiet; then
    print_error "Working directory has uncommitted changes. Please commit or stash them first."
    echo ""
    git status --short
    exit 1
fi

print_success "Working directory is clean."

# Fetch latest changes from origin
print_status "Fetching latest changes from origin..."
if ! git fetch origin; then
    print_error "Failed to fetch from origin. Check your network connection."
    exit 1
fi

# Determine main branch (master or main)
main_branch=""
if git show-ref --verify --quiet refs/heads/master; then
    main_branch="master"
elif git show-ref --verify --quiet refs/heads/main; then
    main_branch="main"
else
    print_error "Could not find master or main branch."
    exit 1
fi

print_status "Main branch detected: $main_branch"

# Switch to main branch
print_status "Switching to $main_branch branch..."
if ! git checkout "$main_branch"; then
    print_error "Failed to checkout $main_branch branch."
    exit 1
fi

# Pull latest changes
print_status "Pulling latest $main_branch changes..."
if ! git pull origin "$main_branch"; then
    print_error "Failed to pull latest changes from $main_branch."
    exit 1
fi

# Switch back to feature branch
print_status "Switching back to feature branch '$current_branch'..."
if ! git checkout "$current_branch"; then
    print_error "Failed to checkout feature branch '$current_branch'."
    exit 1
fi

# Merge main branch into current branch
print_status "Merging $main_branch into '$current_branch'..."
if ! git merge "$main_branch"; then
    print_error "Merge conflict detected!"
    echo ""
    print_warning "To resolve conflicts:"
    echo "   1. Fix conflicts in the affected files"
    echo "   2. Run: git add <resolved-files>"
    echo "   3. Run: git commit"
    echo "   4. Run: git push origin $current_branch"
    echo ""
    print_warning "Or run this script again after resolving conflicts."
    exit 1
fi

# Push updated branch to origin
print_status "Pushing updated branch to origin..."
if ! git push origin "$current_branch"; then
    print_error "Failed to push branch to origin."
    exit 1
fi

# Success message
echo ""
print_success "Successfully synced '$current_branch' with $main_branch!"
print_success "Your branch is now up-to-date and CircleCI should pass."
echo ""
print_status "Summary of changes:"
echo "  • Fetched latest changes from origin"
echo "  • Updated $main_branch branch"
echo "  • Merged $main_branch into $current_branch"
echo "  • Pushed updated $current_branch to origin"
